import cv2
import mediapipe as mp
import numpy as np
import math

class SignLanguageDetector:
    def __init__(self, max_num_hands=2, min_detection_confidence=0.7, min_tracking_confidence=0.5):
        """
        Initialize the sign language detector with MediaPipe
        
        Args:
            max_num_hands: Maximum number of hands to detect
            min_detection_confidence: Minimum confidence for hand detection
            min_tracking_confidence: Minimum confidence for hand tracking
        """
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=max_num_hands,
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Define sign language gestures
        self.sign_names = {
            'thumbs_up': 'Thumbs Up',
            'peace': 'Peace/Victory',
            'ok': 'OK',
            'fist': 'Fist',
            'open_palm': 'Open Palm',
            'pointing': 'Pointing',
            'rock': 'Rock/Horn',
            'call_me': 'Call Me'
        }
        
    def calculate_distance(self, point1, point2):
        """Calculate Euclidean distance between two points"""
        return math.sqrt((point1.x - point2.x)**2 + (point1.y - point2.y)**2)
    
    def is_finger_extended(self, landmarks, finger_tip, finger_pip, finger_mcp):
        """Check if a finger is extended based on landmark positions"""
        tip = landmarks.landmark[finger_tip]
        pip = landmarks.landmark[finger_pip]
        mcp = landmarks.landmark[finger_mcp]
        
        # For thumb, check if tip is further from wrist than pip
        if finger_tip == 4:  # Thumb
            wrist = landmarks.landmark[0]
            tip_to_wrist = self.calculate_distance(tip, wrist)
            pip_to_wrist = self.calculate_distance(pip, wrist)
            return tip_to_wrist > pip_to_wrist
        
        # For other fingers, check if tip is above pip and pip is above mcp
        return tip.y < pip.y < mcp.y
    
    def detect_sign(self, landmarks):
        """
        Detect sign language gesture based on hand landmarks
        
        Args:
            landmarks: Hand landmarks from MediaPipe
            
        Returns:
            str: Detected sign name
        """
        # Finger landmark indices
        # Thumb: 4 (tip), 3 (pip), 2 (mcp)
        # Index: 8 (tip), 6 (pip), 5 (mcp)
        # Middle: 12 (tip), 10 (pip), 9 (mcp)
        # Ring: 16 (tip), 14 (pip), 13 (mcp)
        # Pinky: 20 (tip), 18 (pip), 17 (mcp)
        
        fingers_up = []
        
        # Check thumb
        fingers_up.append(self.is_finger_extended(landmarks, 4, 3, 2))
        
        # Check other fingers
        for finger_tip, finger_pip, finger_mcp in [(8, 6, 5), (12, 10, 9), (16, 14, 13), (20, 18, 17)]:
            fingers_up.append(self.is_finger_extended(landmarks, finger_tip, finger_pip, finger_mcp))
        
        # Detect specific signs based on finger positions
        total_fingers_up = sum(fingers_up)
        
        # Thumbs up: Only thumb extended
        if fingers_up == [True, False, False, False, False]:
            return 'thumbs_up'
        
        # Peace/Victory: Index and middle fingers extended
        elif fingers_up == [False, True, True, False, False]:
            return 'peace'
        
        # OK sign: Thumb and index forming circle, others extended
        elif fingers_up == [True, False, True, True, True]:
            # Additional check for thumb-index proximity for OK sign
            thumb_tip = landmarks.landmark[4]
            index_tip = landmarks.landmark[8]
            distance = self.calculate_distance(thumb_tip, index_tip)
            if distance < 0.05:  # Threshold for "touching"
                return 'ok'
        
        # Fist: No fingers extended
        elif total_fingers_up == 0:
            return 'fist'
        
        # Open palm: All fingers extended
        elif total_fingers_up == 5:
            return 'open_palm'
        
        # Pointing: Only index finger extended
        elif fingers_up == [False, True, False, False, False]:
            return 'pointing'
        
        # Rock/Horn: Index and pinky extended
        elif fingers_up == [False, True, False, False, True]:
            return 'rock'
        
        # Call me: Thumb and pinky extended
        elif fingers_up == [True, False, False, False, True]:
            return 'call_me'
        
        return 'unknown'
    
    def detect_hands_and_signs(self, image):
        """
        Detect hands and recognize sign language gestures
        
        Args:
            image: Input image (BGR format)
            
        Returns:
            tuple: (processed_image, detection_data)
        """
        # Convert BGR to RGB for MediaPipe
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.hands.process(rgb_image)
        
        # Create a copy of the original image for drawing
        output_image = image.copy()
        detection_data = []
        
        if results.multi_hand_landmarks:
            for idx, hand_landmarks in enumerate(results.multi_hand_landmarks):
                # Get image dimensions
                height, width, _ = image.shape
                
                # Get hand label (Left/Right)
                hand_label = results.multi_handedness[idx].classification[0].label
                confidence = results.multi_handedness[idx].classification[0].score
                
                # Detect sign
                detected_sign = self.detect_sign(hand_landmarks)
                sign_name = self.sign_names.get(detected_sign, 'Unknown')
                
                # Calculate bounding box for display
                x_coords = [landmark.x * width for landmark in hand_landmarks.landmark]
                y_coords = [landmark.y * height for landmark in hand_landmarks.landmark]
                
                x_min = int(min(x_coords)) - 20
                x_max = int(max(x_coords)) + 20
                y_min = int(min(y_coords)) - 20
                y_max = int(max(y_coords)) + 20
                
                # Ensure coordinates are within image bounds
                x_min = max(0, x_min)
                y_min = max(0, y_min)
                x_max = min(width, x_max)
                y_max = min(height, y_max)
                
                # Store detection data
                detection_info = {
                    'hand_label': hand_label,
                    'confidence': confidence,
                    'sign': detected_sign,
                    'sign_name': sign_name,
                    'bounding_box': (x_min, y_min, x_max, y_max),
                    'landmarks': hand_landmarks
                }
                detection_data.append(detection_info)
                
                # Draw bounding box
                color = (0, 255, 0) if detected_sign != 'unknown' else (0, 165, 255)
                cv2.rectangle(output_image, (x_min, y_min), (x_max, y_max), color, 2)
                
                # Draw hand landmarks
                self.mp_drawing.draw_landmarks(
                    output_image, hand_landmarks, self.mp_hands.HAND_CONNECTIONS)
                
                # Draw sign label
                label_text = f"{hand_label}: {sign_name}"
                cv2.putText(output_image, label_text, (x_min, y_min - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        return output_image, detection_data

def main():
    """
    Main function to run the sign language detection app
    """
    # Initialize sign language detector
    detector = SignLanguageDetector(max_num_hands=2, min_detection_confidence=0.7)
    
    # Initialize webcam
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("Error: Could not open webcam")
        return
    
    print("Sign Language Detection App Started!")
    print("Supported signs: Thumbs Up, Peace, OK, Fist, Open Palm, Pointing, Rock/Horn, Call Me")
    print("Press 'q' to quit")
    print("Press 's' to save current frame")
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("Error: Could not read frame")
            break
        
        # Flip frame horizontally for mirror effect
        frame = cv2.flip(frame, 1)
        
        # Detect hands and signs
        processed_frame, detection_data = detector.detect_hands_and_signs(frame)
        
        # Display information
        info_text = f"Hands detected: {len(detection_data)}"
        cv2.putText(processed_frame, info_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Display detected signs
        y_offset = 70
        for data in detection_data:
            sign_info = f"{data['hand_label']} Hand: {data['sign_name']}"
            cv2.putText(processed_frame, sign_info, (10, y_offset), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
            y_offset += 30
        
        # Display the frame
        cv2.imshow('Sign Language Detection', processed_frame)
        
        # Handle key presses
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            # Save current frame
            filename = f"sign_detection_frame_{frame_count}.jpg"
            cv2.imwrite(filename, processed_frame)
            print(f"Frame saved as {filename}")
            frame_count += 1
    
    # Clean up
    cap.release()
    cv2.destroyAllWindows()
    print("Sign Language Detection App Closed!")

if __name__ == "__main__":
    main()
